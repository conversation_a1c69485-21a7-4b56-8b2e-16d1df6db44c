/**
 * 解析 curl 命令，提取请求信息
 * @param {string} curlCommand - curl 命令字符串
 * @returns {Object} 解析后的请求信息
 */
function parseCurlCommand(curlCommand) {
    if (!curlCommand || typeof curlCommand !== 'string') {
        return {
            url: '',
            method: 'GET',
            headers: {},
            data: '',
            queryParams: {}
        };
    }

    const result = {
        url: '',
        method: 'GET',
        headers: {},
        data: '',
        queryParams: {}
    };

    // 清理命令，移除多余的空白字符和换行符
    const cleanCommand = curlCommand
        .replace(/\\\s*\n\s*/g, ' ')  // 处理换行连接符
        .replace(/\s+/g, ' ')         // 合并多个空格
        .trim();

    // 提取 URL - 支持多种格式
    let urlMatch = cleanCommand.match(/--url\s+['"]?([^'"\s]+)['"]?/);
    if (!urlMatch) {
        // 如果没有 --url 参数，尝试匹配 curl 后面直接跟的 URL
        urlMatch = cleanCommand.match(/curl\s+['"]?([^'"\s-]+)['"]?/);
    }

    if (urlMatch) {
        const fullUrl = urlMatch[1];
        try {
            const urlObj = new URL(fullUrl);
            result.url = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;

            // 提取查询参数
            urlObj.searchParams.forEach((value, key) => {
                result.queryParams[key] = value;
            });
        } catch (e) {
            result.url = fullUrl;
        }
    }

    // 提取请求方法
    const methodMatch = cleanCommand.match(/--request\s+(\w+)|--X\s+(\w+)|-X\s+(\w+)/i);
    if (methodMatch) {
        result.method = (methodMatch[1] || methodMatch[2] || methodMatch[3]).toUpperCase();
    }

    // 提取请求头
    const headerMatches = cleanCommand.matchAll(/--header\s+['"]([^'"]+)['"]|-H\s+['"]([^'"]+)['"]/g);
    for (const match of headerMatches) {
        const headerStr = match[1] || match[2];
        const colonIndex = headerStr.indexOf(':');
        if (colonIndex > 0) {
            const key = headerStr.substring(0, colonIndex).trim();
            const value = headerStr.substring(colonIndex + 1).trim();
            result.headers[key] = value;
        }
    }

    // 提取请求体数据
    const dataMatch = cleanCommand.match(/--data\s+['"]([^'"]*?)['"]|--data-raw\s+['"]([^'"]*?)['"]|-d\s+['"]([^'"]*?)['"]/s);
    if (dataMatch) {
        result.data = dataMatch[1] || dataMatch[2] || dataMatch[3] || '';
    }

    return result;
}

const testdata = `curl --request POST \
  --url http://*************:8080/mtex/agent/api/park/detail \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "parkIdList": ["DF09A15D-F495-4641-B838-6EDACCBD2163"]
}'`

console.log(parseCurlCommand(testdata));
